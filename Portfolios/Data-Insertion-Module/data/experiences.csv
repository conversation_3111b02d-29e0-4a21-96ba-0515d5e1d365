id,company,position,description,location,achievements,company_url,start_date,end_date,technologies,logo
1,German Research Center for Artificial Intelligence (DFKI),AI Researcher/Engineer,"Driving applied research in Generative AI and multi-agent systems for real-world applications in mobility, science, and enterprise AI.","Berlin, Germany","Built DriveAgent: a multi-modal LLM-based autonomous driving system with real-time sensor fusion and agent orchestration using LangGraph., Fine-tuned models on nuScenes and Waymo datasets; integrated vision-language models like CLIP and Florence-2 for cross-modal understanding., Developed TPRS: a LangGraph-powered multi-agent literature assistant with RAG and citation validation via GraphRAG and PGVector., Enabled full-stack observability and real-time agent traceability using Langfuse; deployed containerized systems on AWS EC2 GPUs., Validated performance gains with +12% accuracy and –15% decision latency over non-agent baselines.",https://www.dfki.de/en/web,July 2024,Present,"Python, FastAPI, LangChain, LangGraph, GraphRAG, HuggingFace, ROS2, <PERSON><PERSON>, A<PERSON> EC2, LLMs, RAG, FAISS, CLIP, PyTorch, Ollama, Langfuse",https://media.licdn.com/dms/image/v2/D4D0BAQE7N1RtnsgvYA/company-logo_200_200/company-logo_200_200/0/1709570346372/dfki_logo?e=**********&v=beta&t=ewkOqWb-ZCRngB0v4MeqQVdq7Nn_Z6XWM987Bgk8t1U
2,Almedia,AI Engineer,"Engineered GenAI-driven solutions for gamified user acquisition, content personalization, and secure transactional workflows using LLMs and deep learning.","Berlin, Germany","Developed dynamic RAG pipelines with LangChain and FAISS for real-time content generation and personalized game prompts, Fine-tuned LLMs on conversational data to build AI-driven onboarding and engagement chatbots, Implemented deep learning–based fraud detection using GANs and VAEs for anomaly scoring, Built scalable ETL pipelines with PySpark and deployed microservices via Docker on AWS EC2 and SageMaker, Evaluated system performance using Precision, AUC-ROC, F1 score, and RAG relevance metrics",https://almedia.co/,February 2024,May 2024,"Python, FastAPI, LangChain, HuggingFace, LlamaIndex, AWS Bedrock, PySpark, Docker, RAG, FAISS, SageMaker, Scrapy, Pandas, Google Gemini",https://media.licdn.com/dms/image/v2/D4E0BAQHFzeFAYzHUvQ/company-logo_200_200/company-logo_200_200/0/1737156106925/almediaco_logo?e=**********&v=beta&t=OCIKWAZOTr2y9eKVoW72O4rJuZLI63BJ5e_ZOHDau2c
3,Synechron,Machine Learning Engineer,Delivered NLP and recommendation system solutions for news analytics and financial services using deep learning and scalable pipelines.,"Pune, India","Built deep learning pipelines for semantic and sentiment analysis of large-scale news and publication datasets using PyTorch and TensorFlow, Developed and deployed a financial portfolio recommender system integrating real-time trends and user risk profiles using Transformers and Sklearn, Reduced model training and inference time by 30% via optimized PySpark workflows on AWS Sagemaker and Dockerized environments",https://www.synechron.com/en-in,February 2023,October 2023,"Python, TensorFlow, PyTorch, Transformers, SQL, NLTK, Keras, AWS Sagemaker, PySpark, Docker, ETL, Pandas, BeautifulSoup, HuggingFace",https://media.licdn.com/dms/image/v2/D4D0BAQHfOptxPIc29g/company-logo_200_200/company-logo_200_200/0/1663673720907/synechron_india_logo?e=**********&v=beta&t=2JifsNEUfik1OCGuCsENfoR1U4En57QWpwPMItUhnLY
4,CitiusTech,Data Scientist,"Built AI pipelines for medical imaging workflows, predictive modeling, and healthcare data analytics to support clinical decision-making.","Pune, India","Implemented end-to-end medical imaging pipeline for NIFTI, DICOM, PACS format handling and ML-based diagnostics, Developed predictive models for patient readmission risk and deployed via microservices using Docker and AWS EC2, Created analytics dashboards in Tableau and automated data workflows with PySpark and AWS S3, improving efficiency by 40%",https://www.citiustech.com/,December 2021,January 2023,"Python, TensorFlow, Keras, Sklearn, PySpark, AWS SageMaker, Docker, ITKSnap, MicroDICOM, Pandas, Numpy, Tableau, SQL",https://media.licdn.com/dms/image/v2/D4D0BAQFvQE1fv2V_1Q/company-logo_200_200/B4DZUFzItlGcAM-/0/1739559046603/citiustech_logo?e=**********&v=beta&t=ygzlMBN4tyTUHYtYo8lPj3zr8BGHITeL3T1BTeSNIRI
5,KPI Partners,Data Engineer,Delivered scalable ETL solutions and data-driven personalization strategies for healthcare outreach using AWS and PySpark.,"Bengaluru, India","Built modular PySpark ETL pipelines to ingest and transform multi-source healthcare data for personalized engagement, Automated pipeline orchestration with AWS Lambda, improving reliability and reducing manual intervention, Enabled targeted outreach analysis using Redshift, Quicksight dashboards, and campaign interaction insights",https://www.kpipartners.com,August 2021,December 2021,"Python, PySpark, SQL, AWS Glue, Redshift, AWS Lambda, API Gateway, S3, AWS Athena, AWS Quicksight",https://media.licdn.com/dms/image/v2/C4D0BAQG0lX97TvDiRg/company-logo_200_200/company-logo_200_200/0/1656759783137/kpi_partners_logo?e=**********&v=beta&t=_5kG7yQtGEqvf-wUXB1yo3XTk8DsaFFJx4L9bZS-oaY
6,Yoamigos Webservice,Full Stack Developer,Developed and maintained web applications for various clients.,"Mumbai, India","Contributed to the development of 10+ client websites, Implemented responsive designs that improved mobile user experience, Collaborated with designers to translate mockups into functional interfaces",https://www.amigoeswebtech.com,May 2018,July 2021,"JavaScript, jQuery, PHP, MySQL, HTML/CSS",https://img.icons8.com/color/96/web-design.png
