id,title,authors,abstract,issue,category,pages,year,keywords,published,external_link,pdf_link,journal,volume,doi
1,Knowledge Scaffolding Recommendation System for Supervising Term Papers,"<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>",TBD,Rank B,Generative AI,Acceptance Ratio 27%,2025,"Large Language Models, Education, Innovation, Recommendation Systems",False,,nan,"20th European Conference on Technology Enhanced Learning (ECTEL 2025). Springer LNCS. 15-19 September 2025. Newcastle and Durham, United Kingdom",Conference,
2,TPRS: AI-Assisted Research Topic Refinement for Distance Learners,"<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>",TBD,Rank A,Generative AI,,2025,"Recommendation Systems, Generative AI, LLM, RAG, VectorDB",<PERSON>alse,,nan,"26th International Conference on Artificial Intelligence in Education (AIED 2025). July 22-26, 2025, Palermo, Italy",Conference,
3,LLM-based Literature Recommender System in Higher Education — A Case Study of Supervising Students’ Term Papers,"<PERSON>a <PERSON>, Nghia Duong-Trung, Rahul R. Bhoyar, Angelin Mary Jose","This paper presents the design and implementation of a Large Language Model (LLM)-based Literature Recommender System (LRS) to support students in higher education during the early stages of their term paper
preparation. The system, named LRS4TP, provides personalized feedback and literature recommendations to
help students formulate research topics and questions, thereby enhancing their critical thinking and research
skills. Unlike existing AI-driven tools, LRS4TP focuses on inspiring students to explore diverse resources and
refine their ideas through iterative feedback rather than automating the writing process. The paper outlines
a case study conducted in a Bachelor of Arts program, where the recommender system assists students in
developing term papers through a combination of natural language processing, sentiment analysis, and expertbased recommendations. Key challenges such as handling creative variations in student submissions, providing
explainable AI recommendations, and ensuring system transparency are addressed. Initial evaluations suggest
that LRS4TP reduces teacher workload while maintaining high-quality feedback, freeing up educators to provide
more meaningful support. The paper concludes with insights into future developments for combining traditional
recommendation techniques with LLM-based approaches to enhance learning in higher education contexts.",Rank A,Generative AI,,2025,"Literature Recommender System, Large Language Models, Higher education, Term paper",True,https://ceur-ws.org/Vol-3994/paper4.pdf,nan,"2nd International Workshop on Generative AI and Learning Analytics (GenAI-LA): Evidence of Impacts on Human Learning, 15th International Learning Analytics and Knowledge Conference (LAK'25). March 3-7, 2025, Dublin, Ireland",Conference,https://ceur-ws.org/Vol-3994/paper4.pdf
4,KaggleGPT: Prompt-based Recommender System for Efficient Dataset Discovery,"Rahul Rajkumar Bhoyar, Xia Wang, Nghia Duong-Trung","Searching appropriate experimental datasets for machine learning projects and reducing the need for one-on-one student-teacher consultations are both challenging. Despite over 50,000 different datasets available across multiple domains on websites like Kaggle, practitioners often need help locating the necessary datasets. Even with the aid of Kaggle’s API and web search functionalities, the search results are not organized meaningfully to a specific context. Recent developments in artificial intelligence (AI) and large language models (LLMs) provide new means of addressing these relevant issues, which were impossible before. This paper introduces KaggleGPT, an LLM- assisted conversational recommender system designed to streamline finding suitable datasets for students’ projects directly from the textual content. The core of KaggleGPT employs a comprehensive approach by integrating profile-based, expert-based, knowledge-based, and multi-criteria-based recommendation engines. Our vision is for educators and students using KaggleGPT to enhance the educational experience and make dataset discovery more efficient and user-friendly.",,Generative AI,207-214,2024,"Kaggle, Recommender System, Prompt-based Recommendation, Large Language Models, Dataset Discovery",True,https://dl.gi.de/items/f392a307-713f-4714-a842-f83dd2d8d755,nan,"EduRS - Recommender Systems in Education at DELFI Conference on Educational Technologies. September 09-11, 2024. Fulda, Germany",Gesellschaft für Informatik e.V.,10.18420/delfi2024-ws-30
