id,title,description,category,date,image_url,technologies,github_url,live_url
1,GraphRAG,"GraphRAG: A scalable Docker-based Retrieval-Augmented Graph system built with LangChain. Integrates knowledge graph embeddings, vector retrieval, and LLM prompts seamlessly. Includes dynamic Docker orchestration, modular components, and clear usage examples.",Generative AI,15/10/2023,https://miro.medium.com/v2/resize:fit:936/0*8h8cHe0EJX5WiKfx.png,"LLM, Docker, RAG",https://github.com/rahulbhoyar1995/GraphRAG,
2,Churn Prediction Using Deep Learning,"Deep learning-based churn prediction model that analyzes customer behavior to identify potential churn. Utilizes neural networks with Keras/TensorFlow for classification. Includes data preprocessing, training, evaluation, and visualization tools.",Deep Learning,01/09/2023,https://www.taboola.com/wp-content/uploads-neo/2025/04/customer-churn.jpg,"Python, TensorFlow, React, Pytorch",https://github.com/rahulbhoyar1995/Churn-Prediction-using-Deep-Learning,
3,Docker Model Runner,"A lightweight Docker-based solution to run and manage machine learning models seamlessly. Ideal for deploying, testing, and scaling ML models in isolated, consistent environments across local and cloud platforms.",Generative AI,20/07/2023,https://miro.medium.com/v2/resize:fit:1200/1*Mma0ia4Yy0nColjVlzxDyA.png,"Docker, Docker Compose, Docker Desktop",https://github.com/rahulbhoyar1995/Docker-Model-Runner,
4,Global Demographics Dashboard,"An interactive Dash and Plotly app visualizing global demographics with maps and charts. Supports continent and country views, real-time updates, and Docker-based deployment with a beautiful dark-themed UI.",Data Science,10/06/2023,https://www.bespoke.xyz/wp-content/uploads/2022/07/World-Population-Day-Power-BI-Dashboard-e1682072473409-993x532.png,"Python, Pandas, Plotly, Flask, Dash",https://github.com/rahulbhoyar1995/Global-Demographics-Dashboard,
5,Web Scraping with Crawl4AI,"A lightning-fast, LLM‑friendly web crawler and scraper in Python—outputs clean Markdown, supports JavaScript, structured extraction, browser control, and async processing—ideal for AI pipelines and data workflows.",Generative AI,05/05/2023,,"python, web scraping, crawl4ai",https://github.com/rahulbhoyar1995/Web-Scraping-with-Crawl4AI,
6,Agentic AI with Python,"A Python-based framework for building Agentic AI systems that can reason, plan, and act autonomously. Includes tools for multi-agent coordination, memory, task execution, and integration with LLMs.",Generative AI,15/04/2023,https://bernardmarr.com/wp-content/uploads/2025/03/Agentic-AI.jpg,"python, Agents, Langgraph, Langchain, Phidata, MCP, LLMs, CrewAI",https://github.com/rahulbhoyar1995/AgenticAI-with-Python,
7,Generative AI with Python,"A comprehensive Python repository showcasing Generative AI techniques, including text, image, and data generation. Includes practical examples, model integrations, and deployment workflows for real-world AI applications.",Generative AI,15/04/2023,https://d3lkc3n5th01x7.cloudfront.net/wp-content/uploads/2024/01/11202325/Generative-AI-Use-Cases-and-Applications-Banner.png,"python, Langgraph, Langchain, LlamaIndex, VectorDB, RAG, LLMs",https://github.com/rahulbhoyar1995/GenAI-with-Python,
8,Pydocify,"An LLM-based library that effortlessly auto-generates docstrings for your Python scripts and modules, enhancing code readability with ease.",Generative AI,15/04/2023,,"LLM, Python, GenAI",https://github.com/rahulbhoyar1995/pydocify,
9,LLMs with Ollama Docker,"A Dockerized setup for running Large Language Models (LLMs) locally using Ollama. Easily deploy, manage, and interact with powerful open-source models for AI development, experimentation, and inference tasks.",Generative AI,15/04/2023,"data:image/png;base64,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","Docker, LLM, Ollama",https://github.com/rahulbhoyar1995/LLMs-with-Ollama-Docker,
10,AWS With Python,"A comprehensive repository demonstrating how to use AWS services with Python, covering automation, deployment, cloud storage, serverless functions, and SDK usage for real-world cloud development and integration tasks.",Cloud,15/04/2023,https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQf2nW0GPxNCoCm8jSly_B9RXQlpCU1qZEiyQ&s,"Python, AWS, Cloud Computing, Automation",https://github.com/rahulbhoyar1995/AWS-With-Python,
11,Microsoft Azure with Python,"A practical repository showcasing how to interact with Microsoft Azure services using Python, including automation, resource management, storage, deployment, serverless computing, and SDK-based cloud integration.",Cloud,15/04/2023,https://www.brainvire.com/blog/wp-content/uploads/2025/05/Unleashing-Python-on-Azure-Serverless-Functions-to-Powerful-Data-Science.webp,"Python, Azure, Cloud Development, Automation",https://github.com/rahulbhoyar1995/Microsoft-Azure-with-Python,
12,Google Cloud with Python,"A hands-on repository demonstrating how to use Google Cloud services with Python, covering automation, storage, APIs, deployment, serverless functions, and integration using Google Cloud SDK and client libraries.",Cloud,15/04/2023,https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQ3-RgrTrCou_yM3G7DDP27S6abRgt-33sczA&s,"Python, Google Cloud, Cloud Computing, DevOps",https://github.com/rahulbhoyar1995/Google-Cloud-with-Python,
13,Working with Databases with Python,"Explore seamless integration of Python and databases through this repository, offering comprehensive guidance, code examples, and best practices for efficient database interactions and management using Python programming language.",Backend Development,15/04/2023,https://miro.medium.com/v2/resize:fit:1400/0*2X6ADMLE2qbPHr3b.png,"Python, Databases, SQLAlchemy",https://github.com/rahulbhoyar1995/Working-with-Databases-with-Python,
14,Anamoly Detection in Transactions,Detect anomalies in transactional data using advanced statistical methods and machine learning algorithms. Enhance fraud detection and anomaly identification in financial transactions for improved security and risk management.,Machine Learning,15/04/2023,https://www.splunk.com/content/dam/splunk2/images/data-insider/anomaly-detection/anomaly-detection-v2hero-short-desktop.jpg,"Python, SKlearn, Jupyter",https://github.com/rahulbhoyar1995/Anomaly-Detection-in-Transactions,
15,Geospatial Data Analysis Projects,"Explore geospatial data analysis through diverse projects. Gain insights into mapping, visualization, and location-based analytics in this repository focused on practical applications and solutions.",Data Science,15/04/2023,,"Python, GeoPandas, Jupyter",https://github.com/rahulbhoyar1995/Geospatial-Data-Analysis-Projects,
16,Hugging Face Tutorials,A comprehensive repository featuring hands-on tutorials and examples for leveraging HuggingFace NLP models and tools. Perfect for beginners and experts aiming to master state-of-the-art AI technologies.,Deep Learning,15/04/2023,,"HuggingFace, Transformers",https://github.com/rahulbhoyar1995/HuggingFace-Tutorials,
17,Kaggle Data Analysis Projects,"This repository hosts Jupyter files for various Kaggle data analysis projects. Explore datasets, analyze trends, and visualize insights. Delve into diverse topics from Bollywood movies to broader data trends. Dive in for engaging analyses and informative visualizations.",Data Science,15/04/2023,,"Data Analysis, Kaggle, Python",https://github.com/rahulbhoyar1995/Kaggle-Data-Analysis-Projects,www.kaggle.com/rrb8695
18,Car Price Predictions,"This project involves developing a predictive model to estimate car prices. Using a Random Forest Regression model, the system considers various features such as year, present price, kilometers driven, and more to predict and display the potential selling price of a car. The Flask web application facilitates user interaction with the model.",Machine Learning,15/04/2023,,"Python, Flask, Machine Learning",https://github.com/rahulbhoyar1995/car-price-predictions-ml-project,
19,Healthcare Data Analysis,"Conduct comprehensive healthcare data analysis to derive insights, improve patient outcomes, and optimize operational efficiency through advanced statistical methodologies and machine learning techniques. Delivering actionable intelligence for informed decision-making.",Data Science,15/04/2023,,"Python, Pandas, Data Analysis",https://github.com/rahulbhoyar1995/Healthcare-Data-Analysis,
20,Streamlit Templates,"A collection of ready-to-use Streamlit templates for building interactive web apps. Includes dashboards, data visualizations, forms, and custom UI components to accelerate rapid prototyping and app development.",Web Development,15/04/2023,,"Python, Streamlit",https://github.com/rahulbhoyar1995/Streamlit-Templates,
21,Streamlit Tutorials,"A repository containing practical examples and guides for utilising Streamlit, a popular Python library for creating interactive web applications with ease. Explore various tutorials to enhance your data visualization and machine learning projects.",Web Development,15/04/2023,,"Python, Streamlit",https://github.com/rahulbhoyar1995/Streamlit-Tutorials,
22,Deploying Streamlit Apps on Streamlit Cloud,Learn to deploy Python apps effortlessly on Streamlit Community Cloud with this comprehensive tutorial repository. Simplified steps for beginners to get their apps up and running quickly.,DevOps/MLOps,15/04/2023,,"Python, Streamlit, Streamlit Cloud",https://github.com/rahulbhoyar1995/Deploy-Apps-on-Streamlit-Cloud-Tutorial,
23,Python in Containers,"Containerized Python application for seamless development, deployment, and scaling. Leverage Docker to isolate dependencies, improve portability, and ensure consistency across environments in Python-based projects.",Backend Development,15/04/2023,,"Docker, Python, Containers",https://github.com/rahulbhoyar1995/Python-in-Containers,
24,Movies REST APIs Project,"A FastAPI application for accessing and managing movie data, offering various endpoints for searching and retrieving movie information from IMDb dataset.",Backend Development,15/04/2023,,"Python, FastAPI, Docker",https://github.com/rahulbhoyar1995/Movies-APIs-Project,
25,Deep Learning with Python,A robust CI/CD pipeline for automating testing and deployment of applications.,Deep Learning,15/04/2023,,"Docker, Jenkins, AWS, Terraform",https://github.com/yourusername/cicd-pipeline,
26,Bash Scripting and Shell Programming,"Tutorials on writing efficient bash scripts, automating tasks, and mastering Unix/Linux shell commands.",Backend Development,15/04/2023,,"Bash, Shell",https://github.com/rahulbhoyar1995/Bash-Scripting-and-Shell-Programming,
27,Named Entity Recognition : Case Study,"Repositiry contains the code for Case study of ""Named-Entity Recognition"" in Natural Language Processing.",Natural Language Processing,15/04/2023,,"NLP, Deep Learning, NER",https://github.com/rahulbhoyar1995/NER-Case-Study,
28,Natural Language Processing with Python,"A comprehensive repository covering Natural Language Processing with Python, including text preprocessing, sentiment analysis, topic modeling, NER, embeddings, and model building using popular NLP libraries like NLTK, spaCy, and Transformers.",Natural Language Processing,15/04/2023,,"Python, NLP, Text Processing, Machine Learning",https://github.com/rahulbhoyar1995/Natural-Language-Processing-with-Python,
29,Computer Vision with Python,"A practical repository demonstrating Computer Vision with Python, featuring image processing, object detection, classification, facial recognition, and deep learning using OpenCV, TensorFlow, and PyTorch libraries.",Deep Learning,15/04/2023,,"Python, Computer Vision, Deep Learning, Image Processing",https://github.com/rahulbhoyar1995/Computer-Vision-with-Python,
30,Machine Learning with Python,"A complete repository showcasing Machine Learning with Python, including data preprocessing, model training, evaluation, and deployment using libraries like scikit-learn, XGBoost, TensorFlow, and Pandas.",Machine Learning,15/04/2023,https://miro.medium.com/v2/resize:fit:1400/1*cG6U1qstYDijh9bPL42e-Q.jpeg,"Python, Machine Learning, AI, Model Training",https://github.com/rahulbhoyar1995/Machine-Learning-with-Python,
31,Robot Automation Framework,"Explore comprehensive tutorials and examples for mastering Robot Framework, an open-source automation framework. Dive into test automation, keyword-driven testing, and more with practical guidance and hands-on projects.",Backend Development,15/04/2023,,"Python, Robot Framework",https://github.com/rahulbhoyar1995/Robot-Framework-Tutorial,
32,Statistics with Python,"A detailed repository exploring Statistics with Python, covering descriptive stats, probability, hypothesis testing, regression, and visualizations using libraries like NumPy, SciPy, and Matplotlib.",Machine Learning,15/04/2023,,"Python, Statistics, Data Analysis, Data Science",https://github.com/rahulbhoyar1995/Statistics-with-Python,
33,Mathematics with Python,"A focused repository demonstrating core mathematical concepts using Python, including algebra, calculus, linear algebra, discrete math, and numerical methods with libraries like SymPy and NumPy.",Machine Learning,15/04/2023,,"Python, Mathematics, Linear Algebra, Numerical Methods",https://github.com/rahulbhoyar1995/Mathematics-with-Python,
34,MLOPs Tutorials,"A practical repository covering MLOps concepts and workflows, including model tracking, CI/CD, deployment, monitoring, and orchestration using tools like MLflow, Docker, and Kubernetes.",DevOps/MLOps,15/04/2023,,"MLOps, Machine Learning, CI/CD, Model Deployment",https://github.com/rahulbhoyar1995/MLOPs-Tutorials,
35,SQL Tutorials,"A comprehensive repository of SQL tutorials covering queries, joins, subqueries, aggregations, indexing, and database design for effective data extraction and manipulation.",Data Science,15/04/2023,,"SQL, Databases, Data Analysis, Querying",https://github.com/rahulbhoyar1995/SQL-Tutorials,
36,Data Analysis with Python,"A hands-on repository for Data Analysis with Python, featuring data wrangling, cleaning, visualization, and statistical analysis using Pandas, NumPy, Matplotlib, and Seaborn.",Data Science,15/04/2023,,"Python, Data Analysis, Pandas, Visualization",https://github.com/rahulbhoyar1995/Data-Analysis-with-Python,
37,Microsoft Excel Automation,"Automate repetitive tasks and streamline workflows in Microsoft Excel with this project. Enhance efficiency, accuracy, and productivity through custom macros, formulas, and VBA scripting solutions.",Backend Development,15/04/2023,,"MS Excel, Automation, Python",https://github.com/rahulbhoyar1995/MS-Excel-Automation-Project,
38,Pyspark Tutorials,"A complete repository for learning PySpark, covering distributed data processing, DataFrames, SQL, RDDs, and big data analytics using Apache Spark with Python.",Data Engineering,15/04/2023,,"PySpark, Big Data, Apache Spark, Data Engineering",https://github.com/rahulbhoyar1995/Pyspark-Tutorials,
39,Web Development Bootcamp,"An end-to-end web development bootcamp repository covering HTML, CSS, JavaScript, Python, Flask/Django, APIs, and deployment for building responsive, full-stack web applications.",Web Development,15/04/2023,,"Web Development, HTML CSS JS, Python, Full Stack",https://github.com/rahulbhoyar1995/Web-Development,
40,To Do App,"Efficiently manage tasks with this Django-powered to-do app. Seamlessly organize, prioritize, and track your daily activities. Enhance productivity and stay on top of your goals effortlessly. Get started now!",Web Development,15/04/2023,,"Python, Django, HTML, CSS",https://github.com/rahulbhoyar1995/to-do-app,
41,Figma Tutorials,"A beginner-friendly repository for learning Figma, covering UI/UX design, prototyping, components, auto layout, and collaborative design for web and mobile applications",UI/UX Design,15/04/2023,,"Figma, UI/UX Design, Prototyping, Product Design",https://github.com/rahulbhoyar1995/Figma-Tutorials,
42,Web Scraping with Python,"A hands-on repository covering web scraping techniques with Python using libraries like BeautifulSoup, Scrapy, and Selenium to extract and process data from websites.",Backend Development,15/04/2023,,"Python, Web Scraping, BeautifulSoup, Automation",https://github.com/rahulbhoyar1995/Web-Scraping-with-Python,
43,Data Structures and Algorithms in Python,"A structured repository focused on data structures and algorithms in Python, covering arrays, linked lists, trees, sorting, searching, recursion, and problem-solving patterns.",Backend Development,15/04/2023,,"Python, DSA, Algorithms, Coding Interview",https://github.com/rahulbhoyar1995/Data-Structures-and-Algorithms-in-Python,
44,Design Patterns in Python,"A comprehensive repository demonstrating classic design patterns in Python, including creational, structural, and behavioral patterns with real-world examples and best practices.",Backend Development,15/04/2023,,"Python, Design Patterns, OOP, Software Architecture",https://github.com/rahulbhoyar1995/Design-Patterns-in-Python,
45,Expense-Tracker,"Expense-Tracker: Easily manage your finances with this intuitive app. Track expenses, set budgets, and gain insights into your spending habits. Stay financially organized effortlessly.",Web Development,09/07/2025,,"Front End, React",https://github.com/rahulbhoyar1995/Expense-Tracker,
