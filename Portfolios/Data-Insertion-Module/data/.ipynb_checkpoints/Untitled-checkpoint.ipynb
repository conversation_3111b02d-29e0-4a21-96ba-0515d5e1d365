{"cells": [{"cell_type": "code", "execution_count": 3, "id": "33ddee78-8f03-4791-957d-8f9cd440ee46", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pandas\n", "  Downloading pandas-2.3.1-cp39-cp39-macosx_11_0_arm64.whl (10.8 MB)\n", "\u001b[K     |████████████████████████████████| 10.8 MB 2.5 MB/s eta 0:00:01\n", "\u001b[?25hRequirement already satisfied: python-dateutil>=2.8.2 in /Users/<USER>/Projects/Portfolios/Data-Insertion-Module/venv/lib/python3.9/site-packages (from pandas) (2.9.0.post0)\n", "Collecting numpy>=1.22.4\n", "  Downloading numpy-2.0.2-cp39-cp39-macosx_14_0_arm64.whl (5.3 MB)\n", "\u001b[K     |████████████████████████████████| 5.3 MB 7.1 MB/s eta 0:00:01\n", "\u001b[?25hCollecting tzdata>=2022.7\n", "  Downloading tzdata-2025.2-py2.py3-none-any.whl (347 kB)\n", "\u001b[K     |████████████████████████████████| 347 kB 18.5 MB/s eta 0:00:01\n", "\u001b[?25hCollecting pytz>=2020.1\n", "  Downloading pytz-2025.2-py2.py3-none-any.whl (509 kB)\n", "\u001b[K     |████████████████████████████████| 509 kB 10.0 MB/s eta 0:00:01\n", "\u001b[?25hRequirement already satisfied: six>=1.5 in /Users/<USER>/Projects/Portfolios/Data-Insertion-Module/venv/lib/python3.9/site-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)\n", "Installing collected packages: tzdata, pytz, numpy, pandas\n", "Successfully installed numpy-2.0.2 pandas-2.3.1 pytz-2025.2 tzdata-2025.2\n", "\u001b[33mWARNING: You are using pip version 21.2.4; however, version 25.1.1 is available.\n", "You should consider upgrading via the '/Users/<USER>/Projects/Portfolios/Data-Insertion-Module/venv/bin/python3 -m pip install --upgrade pip' command.\u001b[0m\n"]}], "source": ["!pip install pandas"]}, {"cell_type": "markdown", "id": "84708ca6-6e21-47a2-b9d5-3c32d7cc0dd2", "metadata": {}, "source": ["## (A) Inserting Projects"]}, {"cell_type": "code", "execution_count": 15, "id": "64e87564-06b0-44ee-a66a-439b66082110", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 0 new unique project(s) inserted successfully!\n"]}], "source": ["from mongoengine import connect, Document, StringField, IntField, DoesNotExist\n", "from dotenv import load_dotenv\n", "import os\n", "import pandas as pd\n", "\n", "# Load credentials from .env\n", "load_dotenv()\n", "MONGODB_DATABASE_URL = os.getenv(\"MONGODB_DATABASE_URL\")   \n", "MONGODB_DATABASE_NAME = os.getenv(\"MONGODB_DATABASE_NAME\")\n", "\n", "# Connect to MongoDB\n", "connect(db=MONGODB_DATABASE_NAME, host=MONGODB_DATABASE_URL)\n", "\n", "# Load CSV\n", "csv_path = \"projects.csv\"  # 🔁 Update this with your CSV file path\n", "projects = pd.read_csv(csv_path)\n", "\n", "# Replace NaN with empty strings\n", "projects = projects.fillna('')\n", "\n", "# Reorder columns and set 'id' as index\n", "projects = projects[[\"id\", \"title\", \"description\", \"category\", \"date\", \"image_url\", \"technologies\", \"github_url\", \"live_url\"]]\n", "projects = projects.set_index(\"id\")\n", "\n", "# Define MongoDB Document\n", "class Project(Document):\n", "    id = IntField(primary_key=True)\n", "    title = StringField()\n", "    description = StringField()\n", "    category = StringField()\n", "    date = StringField()\n", "    image_url = StringField()\n", "    technologies = StringField()\n", "    github_url = StringField()\n", "    live_url = StringField()\n", "\n", "    meta = {'collection': 'projects'}\n", "\n", "# Insert unique records\n", "inserted_count = 0\n", "for idx, row in projects.iterrows():\n", "    try:\n", "        # Skip if record with this ID already exists\n", "        Project.objects.get(id=idx)\n", "    except DoesNotExist:\n", "        # Insert new record\n", "        doc = Project(\n", "            id=idx,\n", "            title=str(row['title']),\n", "            description=str(row['description']),\n", "            category=str(row['category']),\n", "            date=str(row['date']),\n", "            image_url=str(row['image_url']),\n", "            technologies=str(row['technologies']),\n", "            github_url=str(row['github_url']),\n", "            live_url=str(row['live_url']),\n", "        )\n", "        doc.save()\n", "        inserted_count += 1\n", "\n", "print(f\"✅ {inserted_count} new unique project(s) inserted successfully!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "74db5f54-3e16-45ea-9f59-a150e1c32f3c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "43c62218-3a64-400c-9fea-a469c9d1d9ee", "metadata": {}, "source": ["## (B) Insert Certifications"]}, {"cell_type": "code", "execution_count": 25, "id": "264188ec-0aa2-4196-a52a-9b6253ae9aac", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 0 new unique certification(s) inserted successfully!\n"]}], "source": ["from mongoengine import connect, Document, StringField, IntField, DoesNotExist\n", "from dotenv import load_dotenv\n", "import os\n", "import pandas as pd\n", "\n", "# Load environment variables\n", "load_dotenv()\n", "MONGODB_DATABASE_URL = os.getenv(\"MONGODB_DATABASE_URL\")   \n", "MONGODB_DATABASE_NAME = os.getenv(\"MONGODB_DATABASE_NAME\")\n", "\n", "# Connect to MongoDB\n", "connect(db=MONGODB_DATABASE_NAME, host=MONGODB_DATABASE_URL)\n", "\n", "# Load CSV\n", "csv_path = \"certifications.csv\"  # 🔁 Update with your path\n", "certifications = pd.read_csv(csv_path)\n", "\n", "# Replace NaN with empty strings\n", "certifications = certifications.fillna('')\n", "\n", "# Reorder columns (optional, based on your preferred order)\n", "certifications = certifications[[\"id\", \"title\", \"issuer\", \"date\", \"verification_url\", \"icon\"]]\n", "certifications = certifications.set_index(\"id\")\n", "\n", "# Define MongoDB Document\n", "class Certification(Document):\n", "    id = IntField(primary_key=True)\n", "    title = StringField()\n", "    issuer = StringField()\n", "    date = StringField()\n", "    verification_url = StringField()\n", "    icon = StringField()\n", "\n", "    meta = {'collection': 'certifications'}\n", "\n", "# Insert unique records only\n", "inserted_count = 0\n", "for idx, row in certifications.iterrows():\n", "    try:\n", "        # Check if certification with same id exists\n", "        Certification.objects.get(id=idx)\n", "    except DoesNotExist:\n", "        # Insert if not found\n", "        cert = Certification(\n", "            id=idx,\n", "            title=str(row['title']),\n", "            issuer=str(row['issuer']),\n", "            date=str(row['date']),\n", "            verification_url=str(row['verification_url']),\n", "            icon=str(row['icon']),\n", "        )\n", "        cert.save()\n", "        inserted_count += 1\n", "\n", "print(f\"✅ {inserted_count} new unique certification(s) inserted successfully!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "50abcf6c-ff1c-43c7-830d-4f734d72ec1a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d395c597-b495-4140-b769-f92d50d84e12", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "e323ec53-6944-4c1c-b4fe-d318a59daff4", "metadata": {}, "source": ["## (C) Education"]}, {"cell_type": "code", "execution_count": 38, "id": "0043291a-53f9-47da-b150-21ad9b2c7433", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 0 new unique education record(s) inserted successfully!\n"]}], "source": ["from mongoengine import connect, Document, StringField, IntField, DoesNotExist\n", "from dotenv import load_dotenv\n", "import os\n", "import pandas as pd\n", "\n", "# Load environment variables\n", "load_dotenv()\n", "MONGODB_DATABASE_URL = os.getenv(\"MONGODB_DATABASE_URL\")   \n", "MONGODB_DATABASE_NAME = os.getenv(\"MONGODB_DATABASE_NAME\")\n", "\n", "# Connect to MongoDB\n", "connect(db=MONGODB_DATABASE_NAME, host=MONGODB_DATABASE_URL)\n", "\n", "# Load CSV\n", "csv_path = \"education.csv\"  # 🔁 Update to your file path\n", "education = pd.read_csv(csv_path)\n", "\n", "# Replace NaN with empty strings\n", "education = education.fillna('')\n", "\n", "# Reorder columns\n", "education = education[[\n", "    \"id\", \"degree\", \"field\", \"institution\", \"description\",\n", "    \"location\", \"start_date\", \"end_date\", \"achievements\",\n", "    \"institution_url\", \"logo\"\n", "]]\n", "\n", "# Set 'id' as index\n", "education = education.set_index(\"id\")\n", "\n", "# Define MongoDB Document\n", "class Education(Document):\n", "    id = IntField(primary_key=True)\n", "    degree = StringField()\n", "    field = StringField()\n", "    institution = StringField()\n", "    description = StringField()\n", "    location = StringField()\n", "    start_date = IntField()\n", "    end_date = IntField()\n", "    achievements = StringField()\n", "    institution_url = StringField()\n", "    logo = StringField()\n", "\n", "    meta = {'collection': 'education'}\n", "\n", "# Insert unique records\n", "inserted_count = 0\n", "for idx, row in education.iterrows():\n", "    try:\n", "        Education.objects.get(id=idx)\n", "    except DoesNotExist:\n", "        edu = Education(\n", "            id=idx,\n", "            degree=str(row['degree']),\n", "            field=str(row['field']),\n", "            institution=str(row['institution']),\n", "            description=str(row['description']),\n", "            location=str(row['location']),\n", "            start_date=int(row['start_date']),\n", "            end_date=int(row['end_date']),\n", "            achievements=str(row['achievements']),\n", "            institution_url=str(row['institution_url']),\n", "            logo=str(row['logo']),\n", "        )\n", "        edu.save()\n", "        inserted_count += 1\n", "\n", "print(f\"✅ {inserted_count} new unique education record(s) inserted successfully!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "1c2e5344-00f7-41e1-aa2e-74e47c4fc8ea", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "99711619-49bb-47af-a954-25e19d365144", "metadata": {}, "source": ["## (D) Experiences"]}, {"cell_type": "code", "execution_count": 47, "id": "081e2398-f832-451b-8048-ecd1555bf795", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 0 new unique experience record(s) inserted successfully!\n"]}], "source": ["from mongoengine import connect, Document, StringField, IntField, DoesNotExist\n", "from dotenv import load_dotenv\n", "import os\n", "import pandas as pd\n", "\n", "# Load environment variables\n", "load_dotenv()\n", "MONGODB_DATABASE_URL = os.getenv(\"MONGODB_DATABASE_URL\")   \n", "MONGODB_DATABASE_NAME = os.getenv(\"MONGODB_DATABASE_NAME\")\n", "\n", "# Connect to MongoDB\n", "connect(db=MONGODB_DATABASE_NAME, host=MONGODB_DATABASE_URL)\n", "\n", "# Load CSV\n", "csv_path = \"experiences.csv\"  # 🔁 Update this path to your CSV\n", "experiences = pd.read_csv(csv_path)\n", "\n", "# Replace NaN with empty strings\n", "experiences = experiences.fillna('')\n", "\n", "# Reorder columns (for consistency)\n", "experiences = experiences[[\n", "    \"id\", \"company\", \"position\", \"description\", \"location\",\n", "    \"achievements\", \"company_url\", \"start_date\", \"end_date\",\n", "    \"technologies\", \"logo\"\n", "]]\n", "\n", "# Set 'id' as index\n", "experiences = experiences.set_index(\"id\")\n", "\n", "# Define MongoDB Document\n", "class Experience(Document):\n", "    id = IntField(primary_key=True)\n", "    company = StringField()\n", "    position = StringField()\n", "    description = StringField()\n", "    location = StringField()\n", "    achievements = StringField()\n", "    company_url = StringField()\n", "    start_date = StringField()\n", "    end_date = StringField()\n", "    technologies = StringField()\n", "    logo = StringField()\n", "\n", "    meta = {'collection': 'experiences'}\n", "\n", "# Insert only unique records\n", "inserted_count = 0\n", "for idx, row in experiences.iterrows():\n", "    try:\n", "        Experience.objects.get(id=idx)\n", "    except DoesNotExist:\n", "        exp = Experience(\n", "            id=idx,\n", "            company=str(row['company']),\n", "            position=str(row['position']),\n", "            description=str(row['description']),\n", "            location=str(row['location']),\n", "            achievements=str(row['achievements']),\n", "            company_url=str(row['company_url']),\n", "            start_date=str(row['start_date']),\n", "            end_date=str(row['end_date']),\n", "            technologies=str(row['technologies']),\n", "            logo=str(row['logo']),\n", "        )\n", "        exp.save()\n", "        inserted_count += 1\n", "\n", "print(f\"✅ {inserted_count} new unique experience record(s) inserted successfully!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "ae440593-1ad3-429d-808d-6474e57fc833", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "4f722e21-8695-4fdd-848f-2a57ec843bc8", "metadata": {}, "source": ["## (E) Publications"]}, {"cell_type": "code", "execution_count": 78, "id": "656485db-cbc8-4d0d-a2b1-5a794bbb522c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 0 new unique publication(s) inserted successfully!\n"]}], "source": ["from mongoengine import connect, Document, StringField, IntField, BooleanField, DoesNotExist\n", "from dotenv import load_dotenv\n", "import os\n", "import pandas as pd\n", "\n", "# Load .env environment variables\n", "load_dotenv()\n", "MONGODB_DATABASE_URL = os.getenv(\"MONGODB_DATABASE_URL\")   \n", "MONGODB_DATABASE_NAME = os.getenv(\"MONGODB_DATABASE_NAME\")\n", "\n", "# Connect to MongoDB\n", "connect(db=MONGODB_DATABASE_NAME, host=MONGODB_DATABASE_URL)\n", "\n", "# Load CSV\n", "csv_path = \"publications.csv\"  # 🔁 Update this path\n", "publications = pd.read_csv(csv_path)\n", "\n", "# Drop rows with missing 'id' and cast to int\n", "publications = publications[publications['id'].notna()]\n", "publications['id'] = publications['id'].astype(int)\n", "\n", "# Replace remaining NaNs with empty string\n", "publications = publications.fillna('')\n", "\n", "# Reorder columns (optional)\n", "publications = publications[[\n", "    \"id\", \"title\", \"authors\", \"abstract\", \"issue\", \"category\", \n", "    \"pages\", \"year\", \"keywords\", \"published\", \"external_link\", \n", "    \"pdf_link\", \"journal\", \"volume\", \"doi\"\n", "]]\n", "\n", "# Set 'id' as index\n", "publications = publications.set_index(\"id\")\n", "\n", "# Define MongoDB Document\n", "class Publication(Document):\n", "    id = IntField(primary_key=True)\n", "    title = StringField()\n", "    authors = StringField()\n", "    abstract = StringField()\n", "    issue = StringField()\n", "    category = StringField()\n", "    pages = StringField()\n", "    year = IntField()\n", "    keywords = StringField()\n", "    published = BooleanField()\n", "    external_link = StringField()\n", "    pdf_link = StringField()\n", "    journal = StringField()\n", "    volume = StringField()\n", "    doi = StringField()\n", "\n", "    meta = {'collection': 'publications'}\n", "\n", "# Insert unique records\n", "inserted_count = 0\n", "for idx, row in publications.iterrows():\n", "    try:\n", "        Publication.objects.get(id=idx)\n", "    except DoesNotExist:\n", "        pub = Publication(\n", "            id=idx,\n", "            title=str(row['title']),\n", "            authors=str(row['authors']),\n", "            abstract=str(row['abstract']),\n", "            issue=str(row['issue']),\n", "            category=str(row['category']),\n", "            pages=str(row['pages']),\n", "            year=int(row['year']),\n", "            keywords=str(row['keywords']),\n", "            published=bool(row['published']),\n", "            external_link=str(row['external_link']),\n", "            pdf_link=str(row['pdf_link']),\n", "            journal=str(row['journal']),\n", "            volume=str(row['volume']),\n", "            doi=str(row['doi']),\n", "        )\n", "        pub.save()\n", "        inserted_count += 1\n", "\n", "print(f\"✅ {inserted_count} new unique publication(s) inserted successfully!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "8554a13d-36df-4eba-b709-7b5297f81109", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "5ed788dc-cdbf-4903-a319-4b7a76cf4530", "metadata": {}, "source": ["## (F) Skills"]}, {"cell_type": "code", "execution_count": 85, "id": "309c93b0-1cf8-4d36-9f72-122198038abc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 0 new unique skill(s) inserted successfully!\n"]}], "source": ["from mongoengine import connect, Document, StringField, IntField, DoesNotExist\n", "from dotenv import load_dotenv\n", "import os\n", "import pandas as pd\n", "\n", "# Load environment variables from .env\n", "load_dotenv()\n", "MONGODB_DATABASE_URL = os.getenv(\"MONGODB_DATABASE_URL\")   \n", "MONGODB_DATABASE_NAME = os.getenv(\"MONGODB_DATABASE_NAME\")\n", "\n", "# Connect to MongoDB\n", "connect(db=MONGODB_DATABASE_NAME, host=MONGODB_DATABASE_URL)\n", "\n", "# Load CSV (or assume skills DataFrame already exists)\n", "csv_path = \"skills.csv\"  # 🔁 Update path if needed\n", "skills = pd.read_csv(csv_path)\n", "\n", "# Add id column starting from 1 (if not already present)\n", "if 'id' not in skills.columns:\n", "    skills['id'] = range(1, len(skills) + 1)\n", "\n", "# Fill NaNs and reorder columns\n", "skills = skills.fillna('')\n", "skills = skills[['id', 'name', 'level', 'category']]\n", "skills = skills.set_index(\"id\")\n", "\n", "# Define MongoDB Document\n", "class Skill(Document):\n", "    id = IntField(primary_key=True)\n", "    name = StringField()\n", "    level = StringField()\n", "    category = StringField()\n", "\n", "    meta = {'collection': 'skills'}\n", "\n", "# Insert only unique records\n", "inserted_count = 0\n", "for idx, row in skills.iterrows():\n", "    try:\n", "        Skill.objects.get(id=idx)\n", "    except DoesNotExist:\n", "        skill = Skill(\n", "            id=idx,\n", "            name=str(row['name']),\n", "            level=str(row['level']),\n", "            category=str(row['category']),\n", "        )\n", "        skill.save()\n", "        inserted_count += 1\n", "\n", "print(f\"✅ {inserted_count} new unique skill(s) inserted successfully!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "3e87bdcb-fdab-4c6c-baa7-5e3509e19b12", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 5}