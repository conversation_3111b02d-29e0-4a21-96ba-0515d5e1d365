"""
MongoDB CRUD operations using MongoEngine for the portfolio database.
"""

from typing import List, Optional, Dict, Any
from .mongodb_models import (
    ExperienceDocument, SkillDocument, ProjectDocument, 
    EducationDocument, CertificationDocument, PublicationDocument,
    ContactMessageDocument, ChatLogDocument, BlogPostDocument
)
import logging

logger = logging.getLogger(__name__)


# --- SKILLS ---
def get_skills() -> List[SkillDocument]:
    """Get all skills from MongoDB"""
    try:
        return list(SkillDocument.objects.all())
    except Exception as e:
        logger.error(f"Error fetching skills: {str(e)}")
        return []


def create_or_update_skill(name: str, level: str, category: str) -> SkillDocument:
    """Create or update a skill in MongoDB"""
    try:
        # Try to find existing skill by name and category
        skill = SkillDocument.objects(name=name, category=category).first()
        if skill:
            skill.level = level
            skill.save()
        else:
            skill = SkillDocument(name=name, level=level, category=category)
            skill.save()
        return skill
    except Exception as e:
        logger.error(f"Error creating/updating skill: {str(e)}")
        raise e


# --- EXPERIENCES ---
def get_experiences() -> List[ExperienceDocument]:
    """Get all experiences from MongoDB"""
    try:
        return list(ExperienceDocument.objects.all())
    except Exception as e:
        logger.error(f"Error fetching experiences: {str(e)}")
        return []


def create_or_update_experience(**kwargs) -> ExperienceDocument:
    """Create or update an experience in MongoDB"""
    try:
        # If ID is provided, try to update existing record
        if 'id' in kwargs:
            experience = ExperienceDocument.objects(id=kwargs['id']).first()
            if experience:
                for key, value in kwargs.items():
                    if key != 'id' and hasattr(experience, key):
                        setattr(experience, key, value)
                experience.save()
                return experience
        
        # Create new experience (remove id if present)
        kwargs.pop('id', None)
        experience = ExperienceDocument(**kwargs)
        experience.save()
        return experience
    except Exception as e:
        logger.error(f"Error creating/updating experience: {str(e)}")
        raise e


# --- PROJECTS ---
def get_projects() -> List[ProjectDocument]:
    """Get all projects from MongoDB"""
    try:
        return list(ProjectDocument.objects.all())
    except Exception as e:
        logger.error(f"Error fetching projects: {str(e)}")
        return []


def create_or_update_project(**kwargs) -> ProjectDocument:
    """Create or update a project in MongoDB"""
    try:
        # If ID is provided, try to update existing record
        if 'id' in kwargs:
            project = ProjectDocument.objects(id=kwargs['id']).first()
            if project:
                for key, value in kwargs.items():
                    if key != 'id' and hasattr(project, key):
                        setattr(project, key, value)
                project.save()
                return project
        
        # Create new project (remove id if present)
        kwargs.pop('id', None)
        project = ProjectDocument(**kwargs)
        project.save()
        return project
    except Exception as e:
        logger.error(f"Error creating/updating project: {str(e)}")
        raise e


# --- EDUCATION ---
def get_education() -> List[EducationDocument]:
    """Get all education records from MongoDB"""
    try:
        return list(EducationDocument.objects.all())
    except Exception as e:
        logger.error(f"Error fetching education: {str(e)}")
        return []


def create_or_update_education(**kwargs) -> EducationDocument:
    """Create or update an education record in MongoDB"""
    try:
        # If ID is provided, try to update existing record
        if 'id' in kwargs:
            education = EducationDocument.objects(id=kwargs['id']).first()
            if education:
                for key, value in kwargs.items():
                    if key != 'id' and hasattr(education, key):
                        setattr(education, key, value)
                education.save()
                return education
        
        # Create new education record (remove id if present)
        kwargs.pop('id', None)
        education = EducationDocument(**kwargs)
        education.save()
        return education
    except Exception as e:
        logger.error(f"Error creating/updating education: {str(e)}")
        raise e


# --- CERTIFICATIONS ---
def get_certifications() -> List[CertificationDocument]:
    """Get all certifications from MongoDB"""
    try:
        return list(CertificationDocument.objects.all())
    except Exception as e:
        logger.error(f"Error fetching certifications: {str(e)}")
        return []


def create_or_update_certification(**kwargs) -> CertificationDocument:
    """Create or update a certification in MongoDB"""
    try:
        # If ID is provided, try to update existing record
        if 'id' in kwargs:
            certification = CertificationDocument.objects(id=kwargs['id']).first()
            if certification:
                for key, value in kwargs.items():
                    if key != 'id' and hasattr(certification, key):
                        setattr(certification, key, value)
                certification.save()
                return certification
        
        # Create new certification (remove id if present)
        kwargs.pop('id', None)
        certification = CertificationDocument(**kwargs)
        certification.save()
        return certification
    except Exception as e:
        logger.error(f"Error creating/updating certification: {str(e)}")
        raise e


# --- PUBLICATIONS ---
def get_publications() -> List[PublicationDocument]:
    """Get all publications from MongoDB"""
    try:
        return list(PublicationDocument.objects.all())
    except Exception as e:
        logger.error(f"Error fetching publications: {str(e)}")
        return []


def create_or_update_publication(**kwargs) -> PublicationDocument:
    """Create or update a publication in MongoDB"""
    try:
        # If ID is provided, try to update existing record
        if 'id' in kwargs:
            publication = PublicationDocument.objects(id=kwargs['id']).first()
            if publication:
                for key, value in kwargs.items():
                    if key != 'id' and hasattr(publication, key):
                        setattr(publication, key, value)
                publication.save()
                return publication
        
        # Create new publication (remove id if present)
        kwargs.pop('id', None)
        publication = PublicationDocument(**kwargs)
        publication.save()
        return publication
    except Exception as e:
        logger.error(f"Error creating/updating publication: {str(e)}")
        raise e


# --- CONTACT MESSAGES ---
def create_contact_message(name: str, email: str, subject: str, message: str) -> ContactMessageDocument:
    """Create a new contact message in MongoDB"""
    try:
        contact_message = ContactMessageDocument(
            name=name,
            email=email,
            subject=subject,
            message=message
        )
        contact_message.save()
        return contact_message
    except Exception as e:
        logger.error(f"Error creating contact message: {str(e)}")
        raise e


def get_contact_messages(processed: Optional[bool] = None) -> List[ContactMessageDocument]:
    """Get contact messages from MongoDB"""
    try:
        if processed is not None:
            return list(ContactMessageDocument.objects(processed=processed).order_by('-created_at'))
        return list(ContactMessageDocument.objects.all().order_by('-created_at'))
    except Exception as e:
        logger.error(f"Error fetching contact messages: {str(e)}")
        return []


def mark_message_processed(message_id: str) -> Optional[ContactMessageDocument]:
    """Mark a contact message as processed"""
    try:
        message = ContactMessageDocument.objects(id=message_id).first()
        if message:
            message.processed = True
            message.save()
        return message
    except Exception as e:
        logger.error(f"Error marking message as processed: {str(e)}")
        return None


# --- CHAT LOGS ---
def create_chat_log(user_message: str, bot_response: str, session_id: Optional[str] = None) -> ChatLogDocument:
    """Create a new chat log in MongoDB"""
    try:
        chat_log = ChatLogDocument(
            user_message=user_message,
            bot_response=bot_response,
            session_id=session_id
        )
        chat_log.save()
        return chat_log
    except Exception as e:
        logger.error(f"Error creating chat log: {str(e)}")
        raise e


def get_chat_logs(session_id: Optional[str] = None) -> List[ChatLogDocument]:
    """Get chat logs from MongoDB"""
    try:
        if session_id:
            return list(ChatLogDocument.objects(session_id=session_id).order_by('-created_at'))
        return list(ChatLogDocument.objects.all().order_by('-created_at'))
    except Exception as e:
        logger.error(f"Error fetching chat logs: {str(e)}")
        return []


# --- BLOG POSTS ---
def get_blog_posts() -> List[BlogPostDocument]:
    """Get all blog posts from MongoDB"""
    try:
        return list(BlogPostDocument.objects.all().order_by('-created_at'))
    except Exception as e:
        logger.error(f"Error fetching blog posts: {str(e)}")
        return []


def create_blog_post(**kwargs) -> BlogPostDocument:
    """Create a new blog post in MongoDB"""
    try:
        blog_post = BlogPostDocument(**kwargs)
        blog_post.save()
        return blog_post
    except Exception as e:
        logger.error(f"Error creating blog post: {str(e)}")
        raise e
