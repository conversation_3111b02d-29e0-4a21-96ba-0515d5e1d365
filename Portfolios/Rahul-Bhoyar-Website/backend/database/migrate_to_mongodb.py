"""
Data migration script to transfer existing CSV data to MongoDB Atlas.
This script reads CSV files and populates MongoDB collections.
"""

import os
import csv
import json
import logging
from typing import Dict, Any, List
from dotenv import load_dotenv

# Import MongoDB connection and models
from .mongodb_connection import connect_to_mongodb
from .mongodb_crud import (
    create_or_update_skill, create_or_update_experience, create_or_update_project,
    create_or_update_education, create_or_update_certification, create_or_update_publication
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Data directory path
DATA_DIR = os.path.join(os.path.dirname(__file__), "data")


def read_csv(filename: str) -> List[Dict[str, Any]]:
    """
    Read CSV file and return list of dictionaries with cleaned data.
    """
    filepath = os.path.join(DATA_DIR, filename)
    if not os.path.exists(filepath):
        logger.warning(f"CSV file not found: {filepath}")
        return []
    
    rows = []
    try:
        with open(filepath, newline='', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                clean_row = {}
                for key, value in row.items():
                    if value is None:
                        clean_row[key] = None
                        continue

                    value = value.strip()
                    # Convert 'null' or 'none' strings to Python None
                    if value.lower() in ['null', 'none', '']:
                        clean_row[key] = None
                        continue

                    # Handle boolean values
                    if value.lower() == 'true':
                        clean_row[key] = True
                    elif value.lower() == 'false':
                        clean_row[key] = False
                    else:
                        clean_row[key] = value
                
                rows.append(clean_row)
        
        logger.info(f"Successfully read {len(rows)} rows from {filename}")
        return rows
    
    except Exception as e:
        logger.error(f"Error reading CSV file {filename}: {str(e)}")
        return []


def parse_json_field(value: str) -> Any:
    """
    Parse JSON string field or return as string if not valid JSON.
    """
    if not value:
        return None
    
    try:
        # Try to parse as JSON
        return json.loads(value)
    except (json.JSONDecodeError, TypeError):
        # If not valid JSON, return as string
        return value


def convert_to_int(value: Any) -> int:
    """
    Convert value to integer, handling various input types.
    """
    if value is None or value == '':
        return None
    
    try:
        return int(float(str(value)))
    except (ValueError, TypeError):
        return None


def migrate_skills():
    """Migrate skills data from CSV to MongoDB"""
    logger.info("Migrating skills data...")
    skills_data = read_csv("skills.csv")
    
    success_count = 0
    error_count = 0
    
    for i, row in enumerate(skills_data):
        try:
            # Convert level to string if it's numeric
            level = str(row.get('level', '')) if row.get('level') is not None else None
            
            create_or_update_skill(
                name=row.get('name'),
                level=level,
                category=row.get('category')
            )
            success_count += 1
        except Exception as e:
            error_count += 1
            logger.error(f"Error migrating skill row {i+1}: {str(e)}")
            logger.error(f"Row data: {row}")
    
    logger.info(f"Skills migration completed: {success_count} successful, {error_count} errors")


def migrate_experiences():
    """Migrate experiences data from CSV to MongoDB"""
    logger.info("Migrating experiences data...")
    experiences_data = read_csv("experiences.csv")
    
    success_count = 0
    error_count = 0
    
    for i, row in enumerate(experiences_data):
        try:
            # Handle JSON fields
            technologies = parse_json_field(row.get('technologies'))
            achievements = parse_json_field(row.get('achievements'))
            
            # Convert to string if they're lists
            if isinstance(technologies, list):
                technologies = ', '.join(technologies)
            if isinstance(achievements, list):
                achievements = ', '.join(achievements)
            
            create_or_update_experience(
                company=row.get('company'),
                position=row.get('position'),
                description=row.get('description'),
                location=row.get('location'),
                achievements=achievements,
                company_url=row.get('company_url'),
                start_date=row.get('start_date'),
                end_date=row.get('end_date'),
                technologies=technologies,
                logo=row.get('logo')
            )
            success_count += 1
        except Exception as e:
            error_count += 1
            logger.error(f"Error migrating experience row {i+1}: {str(e)}")
            logger.error(f"Row data: {row}")
    
    logger.info(f"Experiences migration completed: {success_count} successful, {error_count} errors")


def migrate_projects():
    """Migrate projects data from CSV to MongoDB"""
    logger.info("Migrating projects data...")
    projects_data = read_csv("projects.csv")
    
    success_count = 0
    error_count = 0
    
    for i, row in enumerate(projects_data):
        try:
            # Handle JSON fields
            technologies = parse_json_field(row.get('technologies'))
            
            # Convert to string if it's a list
            if isinstance(technologies, list):
                technologies = ', '.join(technologies)
            
            create_or_update_project(
                title=row.get('title'),
                description=row.get('description'),
                category=row.get('category'),
                date=row.get('date'),
                image_url=row.get('image_url'),
                technologies=technologies,
                github_url=row.get('github_url'),
                live_url=row.get('live_url')
            )
            success_count += 1
        except Exception as e:
            error_count += 1
            logger.error(f"Error migrating project row {i+1}: {str(e)}")
            logger.error(f"Row data: {row}")
    
    logger.info(f"Projects migration completed: {success_count} successful, {error_count} errors")


def migrate_education():
    """Migrate education data from CSV to MongoDB"""
    logger.info("Migrating education data...")
    education_data = read_csv("education.csv")
    
    success_count = 0
    error_count = 0
    
    for i, row in enumerate(education_data):
        try:
            # Handle JSON fields
            achievements = parse_json_field(row.get('achievements'))
            
            # Convert to string if it's a list
            if isinstance(achievements, list):
                achievements = ', '.join(achievements)
            
            # Convert date fields to integers
            start_date = convert_to_int(row.get('start_date'))
            end_date = convert_to_int(row.get('end_date'))
            
            create_or_update_education(
                degree=row.get('degree'),
                field=row.get('field'),
                institution=row.get('institution'),
                description=row.get('description'),
                location=row.get('location'),
                start_date=start_date,
                end_date=end_date,
                achievements=achievements,
                institution_url=row.get('institution_url'),
                logo=row.get('logo')
            )
            success_count += 1
        except Exception as e:
            error_count += 1
            logger.error(f"Error migrating education row {i+1}: {str(e)}")
            logger.error(f"Row data: {row}")
    
    logger.info(f"Education migration completed: {success_count} successful, {error_count} errors")


def migrate_certifications():
    """Migrate certifications data from CSV to MongoDB"""
    logger.info("Migrating certifications data...")
    certifications_data = read_csv("certifications.csv")

    success_count = 0
    error_count = 0

    for i, row in enumerate(certifications_data):
        try:
            create_or_update_certification(
                title=row.get('title'),
                issuer=row.get('issuer'),
                date=row.get('date'),
                verification_url=row.get('verification_url'),
                icon=row.get('icon')
            )
            success_count += 1
        except Exception as e:
            error_count += 1
            logger.error(f"Error migrating certification row {i+1}: {str(e)}")
            logger.error(f"Row data: {row}")

    logger.info(f"Certifications migration completed: {success_count} successful, {error_count} errors")


def migrate_publications():
    """Migrate publications data from CSV to MongoDB"""
    logger.info("Migrating publications data...")
    publications_data = read_csv("publications.csv")

    success_count = 0
    error_count = 0

    for i, row in enumerate(publications_data):
        try:
            # Handle JSON fields
            keywords = parse_json_field(row.get('keywords'))

            # Convert to string if it's a list
            if isinstance(keywords, list):
                keywords = ', '.join(keywords)

            # Convert year to integer
            year = convert_to_int(row.get('year'))

            # Handle published field
            published = row.get('published')
            if isinstance(published, str):
                published = published.lower() == 'true'
            elif published is None:
                published = True

            create_or_update_publication(
                title=row.get('title'),
                authors=row.get('authors'),
                abstract=row.get('abstract'),
                issue=row.get('issue'),
                category=row.get('category'),
                pages=row.get('pages'),
                year=year,
                keywords=keywords,
                published=published,
                external_link=row.get('external_link'),
                pdf_link=row.get('pdf_link'),
                journal=row.get('journal'),
                volume=row.get('volume'),
                doi=row.get('doi')
            )
            success_count += 1
        except Exception as e:
            error_count += 1
            logger.error(f"Error migrating publication row {i+1}: {str(e)}")
            logger.error(f"Row data: {row}")

    logger.info(f"Publications migration completed: {success_count} successful, {error_count} errors")


def run_full_migration():
    """
    Run complete data migration from CSV files to MongoDB Atlas.
    """
    logger.info("Starting full data migration to MongoDB Atlas...")

    try:
        # Connect to MongoDB
        connect_to_mongodb()
        logger.info("Connected to MongoDB Atlas successfully")

        # Run all migrations
        migrate_skills()
        migrate_experiences()
        migrate_projects()
        migrate_education()
        migrate_certifications()
        migrate_publications()

        logger.info("✅ Full data migration completed successfully!")

    except Exception as e:
        logger.error(f"❌ Migration failed: {str(e)}")
        raise e


if __name__ == "__main__":
    run_full_migration()
