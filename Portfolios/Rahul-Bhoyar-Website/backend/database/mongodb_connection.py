"""
MongoDB connection and configuration using MongoEngine for the portfolio backend.
"""

import os
from mongoengine import connect, disconnect
from dotenv import load_dotenv
import logging

# Set up logging
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# MongoDB configuration
MONGODB_DATABASE_URL = os.getenv("MONGODB_DATABASE_URL")
MONGODB_DATABASE_NAME = os.getenv("MONGODB_DATABASE_NAME", "portfolio_db")

def connect_to_mongodb():
    """
    Establish connection to MongoDB Atlas using MongoEngine.
    """
    try:
        if not MONGODB_DATABASE_URL:
            raise ValueError("MONGODB_DATABASE_URL environment variable is not set")
        
        if not MONGODB_DATABASE_NAME:
            raise ValueError("MONGODB_DATABASE_NAME environment variable is not set")
        
        # Connect to MongoDB Atlas
        connect(
            db=MONGODB_DATABASE_NAME,
            host=MONGODB_DATABASE_URL,
            alias='default'
        )
        
        logger.info(f"Successfully connected to MongoDB Atlas database: {MONGODB_DATABASE_NAME}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB Atlas: {str(e)}")
        raise e

def disconnect_from_mongodb():
    """
    Disconnect from MongoDB Atlas.
    """
    try:
        disconnect()
        logger.info("Disconnected from MongoDB Atlas")
    except Exception as e:
        logger.error(f"Error disconnecting from MongoDB Atlas: {str(e)}")

def get_database_info():
    """
    Get information about the current MongoDB connection.
    """
    return {
        "database_name": MONGODB_DATABASE_NAME,
        "connection_url": MONGODB_DATABASE_URL[:50] + "..." if MONGODB_DATABASE_URL else None,
        "status": "connected"
    }
