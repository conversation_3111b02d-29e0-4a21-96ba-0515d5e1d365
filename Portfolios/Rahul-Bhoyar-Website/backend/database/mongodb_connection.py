"""
MongoDB connection setup for the portfolio backend using MongoEngine.
"""

from mongoengine import connect, disconnect
from dotenv import load_dotenv
import os
from logging_config import setup_logger

# Load environment variables
load_dotenv()

logger = setup_logger("database.mongodb_connection")

# MongoDB configuration
MONGODB_DATABASE_URL = os.getenv("MONGODB_DATABASE_URL")
MONGODB_DATABASE_NAME = os.getenv("MONGODB_DATABASE_NAME")

def connect_mongodb():
    """
    Connect to MongoDB Atlas using MongoEngine.
    """
    try:
        if not MONGODB_DATABASE_URL or not MONGODB_DATABASE_NAME:
            raise ValueError("MongoDB credentials not found in environment variables. Please set MONGODB_DATABASE_URL and MONGODB_DATABASE_NAME in your .env file.")
        
        logger.info(f"🔄 Connecting to MongoDB database: {MONGODB_DATABASE_NAME}")
        connect(db=MONGODB_DATABASE_NAME, host=MONGODB_DATABASE_URL)
        logger.info("✅ Successfully connected to MongoDB Atlas!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to connect to MongoDB: {e}")
        raise e

def disconnect_mongodb():
    """
    Disconnect from MongoDB.
    """
    try:
        logger.info("🔄 Disconnecting from MongoDB...")
        disconnect()
        logger.info("✅ Successfully disconnected from MongoDB!")
    except Exception as e:
        logger.error(f"❌ Failed to disconnect from MongoDB: {e}")
        raise e

def test_mongodb_connection():
    """
    Test MongoDB connection by attempting to connect and disconnect.
    """
    try:
        connect_mongodb()
        disconnect_mongodb()
        return True
    except Exception as e:
        logger.error(f"❌ MongoDB connection test failed: {e}")
        return False
