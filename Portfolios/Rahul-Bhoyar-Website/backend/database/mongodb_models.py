"""
MongoEngine Document models for the portfolio database.
These models match the provided Pydantic schemas for MongoDB Atlas integration.
"""

from mongoengine import Document, StringField, IntField, BooleanField, DateTimeField, ListField
from datetime import datetime
from typing import Optional


class ExperienceDocument(Document):
    """MongoDB Document for experiences collection"""
    meta = {'collection': 'experiences'}
    
    company = StringField(max_length=200)
    position = StringField(max_length=200)
    description = StringField()
    location = StringField(max_length=200)
    achievements = StringField()  # Store as comma-separated string or JSON string
    company_url = StringField(max_length=500)
    start_date = StringField(max_length=50)
    end_date = StringField(max_length=50)
    technologies = StringField()  # Store as comma-separated string or JSON string
    logo = StringField(max_length=500)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)


class SkillDocument(Document):
    """MongoDB Document for skills collection"""
    meta = {'collection': 'skills'}
    
    name = StringField(max_length=100)
    level = StringField(max_length=50)  # Changed to string to match Pydantic schema
    category = StringField(max_length=50)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)


class ProjectDocument(Document):
    """MongoDB Document for projects collection"""
    meta = {'collection': 'projects'}
    
    title = StringField(max_length=300)
    description = StringField()
    category = StringField(max_length=100)
    date = StringField(max_length=50)
    image_url = StringField(max_length=500)
    technologies = StringField()  # Store as comma-separated string or JSON string
    github_url = StringField(max_length=500)
    live_url = StringField(max_length=500)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)


class EducationDocument(Document):
    """MongoDB Document for education collection"""
    meta = {'collection': 'education'}
    
    degree = StringField(max_length=200)
    field = StringField(max_length=200)
    institution = StringField(max_length=300)
    description = StringField()
    location = StringField(max_length=200)
    start_date = IntField()  # Changed to int to match Pydantic schema
    end_date = IntField()    # Changed to int to match Pydantic schema
    achievements = StringField()  # Store as comma-separated string or JSON string
    institution_url = StringField(max_length=500)
    logo = StringField(max_length=500)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)


class CertificationDocument(Document):
    """MongoDB Document for certifications collection"""
    meta = {'collection': 'certifications'}
    
    title = StringField(max_length=300)
    issuer = StringField(max_length=200)
    date = StringField(max_length=50)
    verification_url = StringField(max_length=500)
    icon = StringField(max_length=500)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)


class PublicationDocument(Document):
    """MongoDB Document for publications collection"""
    meta = {'collection': 'publications'}
    
    title = StringField(max_length=500)
    authors = StringField()
    abstract = StringField()
    issue = StringField(max_length=50)
    category = StringField(max_length=100)
    pages = StringField(max_length=50)
    year = IntField()  # Changed to int to match Pydantic schema
    keywords = StringField()  # Store as comma-separated string or JSON string
    published = BooleanField(default=True)
    external_link = StringField(max_length=500)
    pdf_link = StringField(max_length=500)
    journal = StringField(max_length=500)
    volume = StringField(max_length=50)
    doi = StringField(max_length=200)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)


# Additional models for existing functionality
class ContactMessageDocument(Document):
    """MongoDB Document for contact messages"""
    meta = {'collection': 'contact_messages'}
    
    name = StringField(max_length=200, required=True)
    email = StringField(max_length=300, required=True)
    subject = StringField(max_length=500, required=True)
    message = StringField(required=True)
    created_at = DateTimeField(default=datetime.utcnow)
    processed = BooleanField(default=False)


class ChatLogDocument(Document):
    """MongoDB Document for chat logs"""
    meta = {'collection': 'chat_logs'}
    
    user_message = StringField(required=True)
    bot_response = StringField(required=True)
    created_at = DateTimeField(default=datetime.utcnow)
    session_id = StringField(max_length=100)


class BlogPostDocument(Document):
    """MongoDB Document for blog posts"""
    meta = {'collection': 'blog_posts'}
    
    title = StringField(max_length=300, required=True)
    excerpt = StringField()
    date = StringField(max_length=50, required=True)
    author = StringField(max_length=200, required=True)
    category = StringField(max_length=100, required=True)
    tags = ListField(StringField(max_length=50))
    image = StringField(max_length=500)
    featured = BooleanField(default=False)
    content = StringField()
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
