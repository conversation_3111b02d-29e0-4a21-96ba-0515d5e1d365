"""
MongoEngine Document models for the portfolio database.
These models match the provided Pydantic schemas for MongoDB Atlas integration.
"""

from mongoengine import Document, StringField, IntField, BooleanField, DateTimeField
from datetime import datetime
from typing import Optional


class Experiences(Document):
    """
    MongoEngine model for experiences collection.
    Matches the Pydantic schema provided.
    """
    meta = {'collection': 'experiences'}
    
    company = StringField(max_length=200)
    position = StringField(max_length=200)
    description = StringField()
    location = StringField(max_length=200)
    achievements = StringField()  # Store as text, can be parsed as needed
    company_url = StringField(max_length=500)
    start_date = StringField(max_length=50)
    end_date = StringField(max_length=50)
    technologies = StringField()  # Store as comma-separated string
    logo = StringField(max_length=500)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    def save(self, *args, **kwargs):
        self.updated_at = datetime.utcnow()
        return super(Experiences, self).save(*args, **kwargs)


class Skills(Document):
    """
    MongoEngine model for skills collection.
    Matches the Pydantic schema provided.
    """
    meta = {'collection': 'skills'}
    
    name = StringField(max_length=100)
    level = StringField(max_length=50)  # Store as string to match schema
    category = StringField(max_length=50)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    def save(self, *args, **kwargs):
        self.updated_at = datetime.utcnow()
        return super(Skills, self).save(*args, **kwargs)


class Projects(Document):
    """
    MongoEngine model for projects collection.
    Matches the Pydantic schema provided.
    """
    meta = {'collection': 'projects'}
    
    title = StringField(max_length=300)
    description = StringField()
    category = StringField(max_length=100)
    date = StringField(max_length=50)
    image_url = StringField(max_length=500)
    technologies = StringField()  # Store as comma-separated string
    github_url = StringField(max_length=500)
    live_url = StringField(max_length=500)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    def save(self, *args, **kwargs):
        self.updated_at = datetime.utcnow()
        return super(Projects, self).save(*args, **kwargs)


class Education(Document):
    """
    MongoEngine model for education collection.
    Matches the Pydantic schema provided.
    """
    meta = {'collection': 'education'}
    
    degree = StringField(max_length=200)
    field = StringField(max_length=200)
    institution = StringField(max_length=300)
    description = StringField()
    location = StringField(max_length=200)
    start_date = IntField()  # Store as int to match schema
    end_date = IntField()    # Store as int to match schema
    achievements = StringField()  # Store as text
    institution_url = StringField(max_length=500)
    logo = StringField(max_length=500)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    def save(self, *args, **kwargs):
        self.updated_at = datetime.utcnow()
        return super(Education, self).save(*args, **kwargs)


class Certifications(Document):
    """
    MongoEngine model for certifications collection.
    Matches the Pydantic schema provided.
    """
    meta = {'collection': 'certifications'}
    
    title = StringField(max_length=300)
    issuer = StringField(max_length=200)
    date = StringField(max_length=50)
    verification_url = StringField(max_length=500)
    icon = StringField(max_length=500)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    def save(self, *args, **kwargs):
        self.updated_at = datetime.utcnow()
        return super(Certifications, self).save(*args, **kwargs)


class Publications(Document):
    """
    MongoEngine model for publications collection.
    Matches the Pydantic schema provided.
    """
    meta = {'collection': 'publications'}

    title = StringField(max_length=500)
    authors = StringField()
    abstract = StringField()
    issue = StringField(max_length=50)
    category = StringField(max_length=100)
    pages = StringField(max_length=50)
    year = IntField()  # Store as int to match schema
    keywords = StringField()  # Store as comma-separated string or JSON string
    published = BooleanField(default=True)
    external_link = StringField(max_length=500)
    pdf_link = StringField(max_length=500)
    journal = StringField(max_length=500)
    volume = StringField(max_length=50)
    doi = StringField(max_length=200)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    def save(self, *args, **kwargs):
        self.updated_at = datetime.utcnow()
        return super(Publications, self).save(*args, **kwargs)
