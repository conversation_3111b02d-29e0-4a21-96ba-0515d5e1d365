#!/usr/bin/env python3
"""
Script to check if MongoDB setup is ready and provide setup instructions.
"""

import os
from dotenv import load_dotenv

def check_environment_variables():
    """Check if required environment variables are set."""
    load_dotenv()
    
    required_vars = [
        "MONGODB_DATABASE_URL",
        "MONGODB_DATABASE_NAME"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    return missing_vars

def print_setup_instructions():
    """Print setup instructions for MongoDB integration."""
    print("=" * 60)
    print("🔧 MONGODB SETUP INSTRUCTIONS")
    print("=" * 60)
    print()
    print("1. Create a .env file in the backend directory with:")
    print("   MONGODB_DATABASE_URL=mongodb+srv://username:<EMAIL>/")
    print("   MONGODB_DATABASE_NAME=your_database_name")
    print()
    print("2. Make sure your MongoDB Atlas cluster is accessible and contains data")
    print("   in the following collections:")
    print("   - experiences")
    print("   - skills") 
    print("   - projects")
    print("   - education")
    print("   - certifications")
    print("   - publications")
    print()
    print("3. Activate your virtual environment:")
    print("   source venv/bin/activate  # On macOS/Linux")
    print("   venv\\Scripts\\activate     # On Windows")
    print()
    print("4. Install dependencies:")
    print("   pip install -r requirements.txt")
    print()
    print("5. Test the MongoDB connection:")
    print("   python test_mongodb_integration.py")
    print()
    print("6. Start the backend server:")
    print("   python app.py")
    print()

def main():
    """Main function to check setup and provide instructions."""
    print("🔍 Checking MongoDB setup...")
    
    # Check if .env file exists
    env_file = ".env"
    if not os.path.exists(env_file):
        print(f"❌ {env_file} file not found!")
        print_setup_instructions()
        return False
    
    # Check environment variables
    missing_vars = check_environment_variables()
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print_setup_instructions()
        return False
    
    print("✅ Environment variables are set!")
    
    # Check if mongoengine is available
    try:
        import mongoengine
        print("✅ mongoengine is available!")
    except ImportError:
        print("❌ mongoengine not installed!")
        print("   Run: pip install -r requirements.txt")
        return False
    
    print()
    print("🎉 MongoDB setup looks good!")
    print("📝 Next steps:")
    print("   1. Run: python test_mongodb_integration.py")
    print("   2. If tests pass, run: python app.py")
    print()
    
    return True

if __name__ == "__main__":
    main()
