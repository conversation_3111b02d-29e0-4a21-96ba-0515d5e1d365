#!/usr/bin/env python3
"""
Test script to verify MongoDB integration works correctly.
This script tests the MongoDB connection and CRUD operations.
"""

import sys
import os
from dotenv import load_dotenv

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

from database.mongodb_connection import connect_mongodb, test_mongodb_connection
from database import mongodb_crud
from logging_config import setup_logger

logger = setup_logger("test_mongodb_integration")

def test_mongodb_connection_basic():
    """Test basic MongoDB connection."""
    logger.info("🧪 Testing MongoDB connection...")
    try:
        result = test_mongodb_connection()
        if result:
            logger.info("✅ MongoDB connection test passed!")
            return True
        else:
            logger.error("❌ MongoDB connection test failed!")
            return False
    except Exception as e:
        logger.error(f"❌ MongoDB connection test error: {e}")
        return False

def test_mongodb_crud_operations():
    """Test MongoDB CRUD operations."""
    logger.info("🧪 Testing MongoDB CRUD operations...")
    
    try:
        # Connect to MongoDB
        connect_mongodb()
        
        # Test each CRUD operation
        tests = [
            ("Skills", mongodb_crud.get_skills),
            ("Experiences", mongodb_crud.get_experiences),
            ("Education", mongodb_crud.get_education),
            ("Certifications", mongodb_crud.get_certifications),
            ("Projects", mongodb_crud.get_projects),
            ("Publications", mongodb_crud.get_publications),
        ]
        
        all_passed = True
        for name, func in tests:
            try:
                logger.info(f"  🔍 Testing {name}...")
                result = func()
                logger.info(f"  ✅ {name}: Retrieved {len(result)} records")
                
                # Print sample data if available
                if result and len(result) > 0:
                    sample = result[0]
                    logger.info(f"  📋 Sample {name} data keys: {list(sample.keys())}")
                
            except Exception as e:
                logger.error(f"  ❌ {name} test failed: {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ MongoDB CRUD operations test error: {e}")
        return False

def test_project_by_id():
    """Test getting a specific project by ID."""
    logger.info("🧪 Testing project by ID...")
    
    try:
        # First get all projects to get a valid ID
        projects = mongodb_crud.get_projects()
        if not projects:
            logger.warning("⚠️  No projects found, skipping project by ID test")
            return True
        
        # Test with the first project's ID
        project_id = projects[0]["id"]
        logger.info(f"  🔍 Testing with project ID: {project_id}")
        
        project = mongodb_crud.get_project_by_id(project_id)
        if project:
            logger.info(f"  ✅ Project by ID test passed: {project['title']}")
            return True
        else:
            logger.error("  ❌ Project by ID test failed: No project returned")
            return False
            
    except Exception as e:
        logger.error(f"❌ Project by ID test error: {e}")
        return False

def main():
    """Run all MongoDB integration tests."""
    logger.info("🚀 Starting MongoDB Integration Tests...")
    
    # Check if MongoDB credentials are available
    mongodb_url = os.getenv("MONGODB_DATABASE_URL")
    mongodb_name = os.getenv("MONGODB_DATABASE_NAME")
    
    if not mongodb_url or not mongodb_name:
        logger.error("❌ MongoDB credentials not found in environment variables!")
        logger.error("   Please set MONGODB_DATABASE_URL and MONGODB_DATABASE_NAME in your .env file")
        return False
    
    logger.info(f"📊 MongoDB Database: {mongodb_name}")
    
    # Run tests
    tests = [
        ("MongoDB Connection", test_mongodb_connection_basic),
        ("MongoDB CRUD Operations", test_mongodb_crud_operations),
        ("Project by ID", test_project_by_id),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        if test_func():
            passed += 1
            logger.info(f"✅ {test_name} PASSED")
        else:
            logger.error(f"❌ {test_name} FAILED")
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info(f"TEST SUMMARY")
    logger.info(f"{'='*50}")
    logger.info(f"Passed: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 All MongoDB integration tests passed!")
        return True
    else:
        logger.error(f"💥 {total - passed} test(s) failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
