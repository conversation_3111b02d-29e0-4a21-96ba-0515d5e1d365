# Portfolio Backend Database Setup

This document describes the MongoDB Atlas database implementation for the Rahul Bhoyar Portfolio Backend.

## Overview

The backend now uses MongoDB Atlas as the cloud database to store all portfolio data including:
- Skills
- Work Experience
- Education
- Certifications
- Projects
- Publications
- Blog Posts
- Contact Messages
- Chat Logs

## Database Structure

### Collections

1. **skills** - Technical skills with proficiency levels
2. **experiences** - Work experience and job history
3. **education** - Educational background
4. **certifications** - Professional certifications
5. **projects** - Portfolio projects
6. **publications** - Research publications and papers
7. **blog_posts** - Blog articles and posts (future implementation)
8. **contact_messages** - Messages from contact form (future implementation)
9. **chat_logs** - Chat interactions with the AI assistant (future implementation)

## Setup Instructions

### 1. Install Dependencies

```bash
pip install mongoengine python-dotenv
```

### 2. Configure Environment Variables

Create a `.env` file in the backend directory with your MongoDB Atlas credentials:

```bash
MONGODB_DATABASE_URL=mongodb+srv://username:<EMAIL>/
MONGODB_DATABASE_NAME=your_database_name
```

### 3. Test MongoDB Connection

```bash
python check_mongodb_setup.py
python test_mongodb_integration.py
```

```bash
python database/init_db.py --reset
```

### 3. Test Database

Run the test script to verify everything is working:

```bash
python test_database.py
```

## API Endpoints

### Public Endpoints
- `GET /api/projects` - Get all projects
- `GET /api/projects/{id}` - Get specific project
- `GET /api/skills` - Get all skills
- `GET /api/experience` - Get work experience
- `GET /api/education` - Get education history
- `GET /api/certifications` - Get certifications
- `GET /api/publications` - Get publications
- `GET /api/blog` - Get blog posts
- `GET /api/blog/{id}` - Get specific blog post
- `POST /api/contact` - Submit contact message
- `POST /api/chat` - Chat with AI assistant

### Admin Endpoints
- `GET /api/admin/contact-messages` - Get all contact messages
- `GET /api/admin/chat-logs` - Get all chat logs
- `PUT /api/admin/contact-messages/{id}/processed` - Mark message as processed

## Database Configuration

The database configuration is in `database/mongodb_connection.py`. It uses MongoDB Atlas as the cloud database service.

Required environment variables:

```bash
MONGODB_DATABASE_URL=mongodb+srv://username:<EMAIL>/
MONGODB_DATABASE_NAME=your_database_name
```

## Data Migration

The data has been migrated from CSV files to MongoDB Atlas. The backend now retrieves data directly from the MongoDB collections using the provided Pydantic schemas.

## Features

### Contact Form Storage
- All contact form submissions are stored in the database
- Messages can be marked as processed
- Admin endpoint to view all messages

### Chat Log Storage
- All chat interactions are logged
- Optional session tracking
- Admin endpoint to view chat history

### Data Persistence
- All portfolio data is now persistent
- Easy to backup (just copy the SQLite file)
- Can be easily migrated to other databases if needed

## File Structure

```
backend/
├── database/
│   ├── __init__.py
│   ├── mongodb_connection.py    # MongoDB connection management
│   ├── mongodb_models.py        # MongoEngine document models
│   └── mongodb_crud.py          # MongoDB operations
├── test_mongodb_integration.py  # MongoDB integration test script
├── check_mongodb_setup.py       # Setup verification script
├── app.py                       # Updated with MongoDB initialization
└── routes/api.py                # Updated API routes using MongoDB
```

## Troubleshooting

### Common Issues

1. **Import errors**: Make sure mongoengine is installed (`pip install mongoengine`)
2. **Connection errors**: Check your MongoDB Atlas credentials and network connectivity
3. **Authentication errors**: Verify your MongoDB Atlas username and password
4. **Environment variable errors**: Ensure MONGODB_DATABASE_URL and MONGODB_DATABASE_NAME are set in .env file

### Logs

The application logs database initialization status on startup. Check the console output for any database-related errors.

## Future Enhancements

- Add database migrations using Alembic
- Implement database backup/restore functionality
- Add data validation and constraints
- Implement user authentication for admin endpoints
- Add database performance monitoring
