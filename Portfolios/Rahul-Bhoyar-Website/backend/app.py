from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from routes.api import router as api_router
from database.mongodb_connection import connect_mongodb, disconnect_mongodb
from logging_config import setup_logger

logger = setup_logger("main")

app = FastAPI(
    title="<PERSON>hul Bhoyar Portfolio API",
    description="Backend API for <PERSON>hul Bhoyar's Portfolio Website",
    version="1.0.0"
)

# Initialize MongoDB connection on startup
@app.on_event("startup")
async def startup_event():
    """Initialize MongoDB connection on application startup."""
    try:
        logger.info("🔄 Initializing MongoDB connection...")
        connect_mongodb()
        logger.info("✅ MongoDB connection initialized successfully!")
    except Exception as e:
        logger.error(f"❌ Failed to initialize MongoDB connection: {e}")
        # Don't fail the startup, just log the error
        pass

@app.on_event("shutdown")
async def shutdown_event():
    """Disconnect from MongoDB on application shutdown."""
    try:
        logger.info("🔄 Disconnecting from MongoDB...")
        disconnect_mongodb()
        logger.info("✅ MongoDB disconnected successfully!")
    except Exception as e:
        logger.error(f"❌ Failed to disconnect from MongoDB: {e}")
    logger.info("🛑 Application shutdown.")
    
# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api_router)

if __name__ == '__main__':
    uvicorn.run("app:app", host="0.0.0.0", port=8000, reload=True)
